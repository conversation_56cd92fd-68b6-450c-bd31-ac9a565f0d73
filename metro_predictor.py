import os
import numpy as np
import pandas as pd
import geopandas as gpd
import torch
import torch.nn as nn
import torch.nn.functional as F
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# Configuration
DATA_DIR = r"C:\Users\<USER>\Desktop\prev1"
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
MODEL_PATH = os.path.join(DATA_DIR, 'best_advanced_metro_model.pth')

# Model parameters (must match training configuration)
HIDDEN_DIM = 256
EMBED_DIM = 64

print(f"Using device: {DEVICE}")

# ------------------------------
# Model Architecture (copied from training file)
# ------------------------------

class MultiHeadAttention(nn.Module):
    def __init__(self, d_model, num_heads, dropout=0.1):
        super().__init__()
        assert d_model % num_heads == 0
        
        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads
        
        self.W_q = nn.Linear(d_model, d_model)
        self.W_k = nn.Linear(d_model, d_model)
        self.W_v = nn.Linear(d_model, d_model)
        self.W_o = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, query, key, value, mask=None):
        batch_size = query.size(0)
        
        Q = self.W_q(query).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        K = self.W_k(key).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        V = self.W_v(value).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        
        scores = torch.matmul(Q, K.transpose(-2, -1)) / np.sqrt(self.d_k)
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        context = torch.matmul(attention_weights, V)
        context = context.transpose(1, 2).contiguous().view(batch_size, -1, self.d_model)
        
        output = self.W_o(context)
        return output

class ResidualBlock(nn.Module):
    def __init__(self, input_dim, hidden_dim, dropout=0.2):
        super().__init__()
        self.linear1 = nn.Linear(input_dim, hidden_dim)
        self.linear2 = nn.Linear(hidden_dim, input_dim)
        self.norm1 = nn.LayerNorm(input_dim)
        self.norm2 = nn.LayerNorm(input_dim)
        self.dropout = nn.Dropout(dropout)
        self.activation = nn.GELU()
    
    def forward(self, x):
        residual = x
        x = self.norm1(x)
        x = self.linear1(x)
        x = self.activation(x)
        x = self.dropout(x)
        x = self.linear2(x)
        x = self.dropout(x)
        x = x + residual
        x = self.norm2(x)
        return x

class AdvancedMetroFlowPredictor(nn.Module):
    def __init__(self, station_flow_input_dim, od_flow_input_dim, num_stations, num_grids=70000, num_hours=24, hidden_dim=256, embed_dim=64):
        super().__init__()
        self.hidden_dim = hidden_dim
        self.embed_dim = embed_dim
        self.num_stations = num_stations
        self.num_grids = num_grids
        self.num_hours = num_hours
        
        # Embeddings
        self.hour_embedding = nn.Embedding(num_hours, embed_dim)
        self.station_embedding = nn.Embedding(num_stations, embed_dim)
        self.grid_embedding = nn.Embedding(num_grids, embed_dim)
        
        self.station_hour_embedding = nn.Parameter(
            torch.randn(num_stations, num_hours, embed_dim) * 0.1
        )
        
        self.num_regions = min(50, num_stations // 8)
        self.region_embedding = nn.Embedding(self.num_regions, embed_dim)
        self.station_to_region = nn.Embedding(num_stations, self.num_regions)
        self.station_to_region.weight.data = F.softmax(
            torch.randn(num_stations, self.num_regions), dim=1
        )
        
        # Feature extractors
        station_total_dim = station_flow_input_dim + embed_dim * 3
        self.station_feature_extractor = nn.Sequential(
            nn.Linear(station_total_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(0.3),
            ResidualBlock(hidden_dim, hidden_dim * 2),
            ResidualBlock(hidden_dim, hidden_dim * 2),
            ResidualBlock(hidden_dim, hidden_dim * 2)
        )
        
        od_total_dim = od_flow_input_dim + embed_dim * 6
        self.od_feature_extractor = nn.Sequential(
            nn.Linear(od_total_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(0.3),
            ResidualBlock(hidden_dim, hidden_dim * 2),
            ResidualBlock(hidden_dim, hidden_dim * 2),
            ResidualBlock(hidden_dim, hidden_dim * 2)
        )
        
        # Cross-attention and fusion
        self.od_cross_attention = MultiHeadAttention(hidden_dim // 2, num_heads=8)
        self.od_fusion = nn.Sequential(
            nn.Linear(hidden_dim + hidden_dim // 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(0.2)
        )
        
        # Prediction heads
        self.in_predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.GELU(),
            nn.Linear(hidden_dim // 4, 1)
        )
        
        self.out_predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.GELU(),
            nn.Linear(hidden_dim // 4, 1)
        )
        
        self.od_predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.GELU(),
            nn.Linear(hidden_dim // 4, 1)
        )
    
    def forward_station_flow(self, x, station_ids, hours):
        """Forward pass for station flow prediction"""
        station_embeds = self.station_embedding(station_ids)
        hour_embeds = self.hour_embedding(hours % 24)
        station_hour_embeds = self.station_hour_embedding[station_ids, hours % 24]
        station_region_weights = self.station_to_region(station_ids)
        region_embeds = torch.matmul(station_region_weights, self.region_embedding.weight)
        
        combined_features = torch.cat([
            x, station_embeds, hour_embeds + station_hour_embeds, region_embeds
        ], dim=1)
        
        features = self.station_feature_extractor(combined_features)
        in_pred = self.in_predictor(features)
        out_pred = self.out_predictor(features)
        
        return in_pred.squeeze(-1), out_pred.squeeze(-1)
    
    def forward_od_flow(self, x, o_station_ids, d_station_ids, hours):
        """Forward pass for OD flow prediction"""
        o_station_embeds = self.station_embedding(o_station_ids)
        d_station_embeds = self.station_embedding(d_station_ids)
        o_hour_embeds = self.hour_embedding(hours % 24)
        d_hour_embeds = self.hour_embedding(hours % 24)
        
        o_station_hour_embeds = self.station_hour_embedding[o_station_ids, hours % 24]
        d_station_hour_embeds = self.station_hour_embedding[d_station_ids, hours % 24]
        
        o_station_region_weights = self.station_to_region(o_station_ids)
        d_station_region_weights = self.station_to_region(d_station_ids)
        o_region_embeds = torch.matmul(o_station_region_weights, self.region_embedding.weight)
        d_region_embeds = torch.matmul(d_station_region_weights, self.region_embedding.weight)
        
        combined_features = torch.cat([
            x, o_station_embeds, d_station_embeds,
            o_hour_embeds + o_station_hour_embeds,
            d_hour_embeds + d_station_hour_embeds,
            o_region_embeds, d_region_embeds
        ], dim=1)
        
        features = self.od_feature_extractor(combined_features)
        
        mid_point = features.size(1) // 2
        origin_features = features[:, :mid_point].unsqueeze(1)
        dest_features = features[:, mid_point:].unsqueeze(1)
        
        attended_features = self.od_cross_attention(origin_features, dest_features, dest_features)
        attended_features = attended_features.squeeze(1)
        
        combined_features = torch.cat([features, attended_features], dim=1)
        fused_features = self.od_fusion(combined_features)
        
        od_pred = self.od_predictor(fused_features)
        return od_pred.squeeze(-1)

# ------------------------------
# Utility Functions
# ------------------------------

def load_shapefile(fname):
    return gpd.read_file(os.path.join(DATA_DIR, fname))

def build_station_graph(station_connect_csv):
    """Build station graph and return station mapping"""
    df = pd.read_csv(os.path.join(DATA_DIR, station_connect_csv))
    df['station_1'] = df['station_1'].str.replace(r'_.*$', '', regex=True)
    df['station_2'] = df['station_2'].str.replace(r'_.*$', '', regex=True)
    stations = pd.unique(df[['station_1','station_2']].values.ravel())
    idx = {s: i for i, s in enumerate(stations)}
    return stations, idx

def aggregate_station_features(grid_gdf, grid_feats_df, station_idx):
    """Aggregate grid features around each station within 1.5km radius"""
    station_feats = []
    
    for station_name in station_idx.keys():
        station_grids = grid_gdf[grid_gdf['station'] == station_name]
        
        if len(station_grids) > 0:
            grid_ids = station_grids['grid_id'].values
            station_grid_feats = grid_feats_df[grid_feats_df['id'].isin(grid_ids)]
            
            if len(station_grid_feats) > 0:
                numeric_cols = station_grid_feats.select_dtypes(include=[np.number]).columns
                numeric_cols = [col for col in numeric_cols if col != 'id']
                agg_feats = station_grid_feats[numeric_cols].mean()
                agg_feats['station'] = station_name
                station_feats.append(agg_feats)
            else:
                numeric_cols = grid_feats_df.select_dtypes(include=[np.number]).columns
                numeric_cols = [col for col in numeric_cols if col != 'id']
                zero_feats = pd.Series(0.0, index=numeric_cols)
                zero_feats['station'] = station_name
                station_feats.append(zero_feats)
        else:
            numeric_cols = grid_feats_df.select_dtypes(include=[np.number]).columns
            numeric_cols = [col for col in numeric_cols if col != 'id']
            zero_feats = pd.Series(0.0, index=numeric_cols)
            zero_feats['station'] = station_name
            station_feats.append(zero_feats)
    
    return pd.DataFrame(station_feats)

# ------------------------------
# Predictor Class
# ------------------------------

class MetroFlowPredictor:
    def __init__(self, model_path=MODEL_PATH):
        """Initialize the predictor with trained model"""
        self.model_path = model_path
        self.model = None
        self.station_idx = None
        self.stations = None
        self.station_feats = None
        self.station_scaler = StandardScaler()
        self.od_scaler = StandardScaler()

        print("Loading data and model...")
        self._load_data()
        self._load_model()
        print("Predictor initialized successfully!")

    def _load_data(self):
        """Load necessary data files"""
        try:
            # Load station graph
            self.stations, self.station_idx = build_station_graph('station_connect_2023.csv')
            print(f"Loaded {len(self.stations)} stations")

            # Load grid features
            grid_feats = pd.read_csv(os.path.join(DATA_DIR, 'leti_data.csv'))
            print(f"Loaded grid features: {len(grid_feats)} records")

            # Try to load spatial data for 1.5km aggregation
            try:
                in_gdf = load_shapefile('in_500_with_coords.shp')
                grid_gdf = in_gdf[['grid_id', 'station']].drop_duplicates()
                self.station_feats = aggregate_station_features(grid_gdf, grid_feats, self.station_idx)
                print("Using 1.5km radius feature aggregation")
            except Exception as e:
                print(f"Could not use spatial aggregation: {e}")
                # Fallback to simple aggregation would go here
                raise

            # Fit scalers using existing data
            in_data = pd.read_csv(os.path.join(DATA_DIR, 'in_500.csv'))
            od_df = pd.read_csv(os.path.join(DATA_DIR, 'updated北京市_subway_od_2024_modified3_deduplicated.csv'))

            # Fit station scaler
            numeric_cols = self.station_feats.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 0:
                self.station_scaler.fit(self.station_feats[numeric_cols])

            # Fit OD scaler with sample data
            if len(od_df) > 0:
                sample_od = od_df.sample(n=min(1000, len(od_df)), random_state=42)
                od_features = []
                for _, row in sample_od.iterrows():
                    od_features.append([
                        row['surface_distance'],
                        row['translate'],
                        row['time'],
                        row['wait_time']
                    ])
                self.od_scaler.fit(np.array(od_features))

        except Exception as e:
            print(f"Error loading data: {e}")
            raise

    def _load_model(self):
        """Load the trained model"""
        try:
            # Calculate input dimensions
            station_feature_dim = len(self.station_feats.select_dtypes(include=[np.number]).columns)
            station_input_dim = station_feature_dim + 6  # station_feats + temporal
            od_input_dim = station_feature_dim * 2 + 4 + 6 + 2  # origin + dest + od_feats + temporal + interaction

            # Initialize model
            self.model = AdvancedMetroFlowPredictor(
                station_flow_input_dim=station_input_dim,
                od_flow_input_dim=od_input_dim,
                num_stations=len(self.stations),
                hidden_dim=HIDDEN_DIM,
                embed_dim=EMBED_DIM
            )

            # Load trained weights
            if os.path.exists(self.model_path):
                self.model.load_state_dict(torch.load(self.model_path, map_location=DEVICE))
                self.model.to(DEVICE)
                self.model.eval()
                print(f"Model loaded from {self.model_path}")
            else:
                raise FileNotFoundError(f"Model file not found: {self.model_path}")

        except Exception as e:
            print(f"Error loading model: {e}")
            raise

    def _get_station_features(self, station):
        """Get features for a specific station"""
        station_feats = self.station_feats[self.station_feats['station'] == station]
        if len(station_feats) > 0:
            numeric_cols = station_feats.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 0:
                feats = station_feats[numeric_cols].values.flatten()
                feats = self.station_scaler.transform(feats.reshape(1, -1)).flatten()
                return feats
        # Return zeros if station not found
        numeric_cols = self.station_feats.select_dtypes(include=[np.number]).columns
        return np.zeros(len(numeric_cols))

    def _get_temporal_features(self, hour):
        """Get temporal features for given hour"""
        hour_of_day = hour % 24
        day_of_week = (hour // 24) % 7
        is_weekend = 1.0 if day_of_week >= 5 else 0.0
        is_peak_hour = 1.0 if hour_of_day in [7, 8, 9, 17, 18, 19] else 0.0

        # Cyclical encoding
        hour_sin = np.sin(2 * np.pi * hour_of_day / 24)
        hour_cos = np.cos(2 * np.pi * hour_of_day / 24)
        day_sin = np.sin(2 * np.pi * day_of_week / 7)
        day_cos = np.cos(2 * np.pi * day_of_week / 7)

        return np.array([hour_sin, hour_cos, day_sin, day_cos, is_weekend, is_peak_hour])

    def predict_station_flow(self, station, hour):
        """
        Predict inflow and outflow for a specific station at given hour

        Args:
            station (str): Station name
            hour (int): Hour (0-23 for hour of day, or absolute hour)

        Returns:
            tuple: (predicted_inflow, predicted_outflow)
        """
        if station not in self.station_idx:
            print(f"Warning: Station '{station}' not found in station index")
            return 0.0, 0.0

        try:
            # Get station features
            station_feats = self._get_station_features(station)
            temporal_feats = self._get_temporal_features(hour)

            # Combine features
            X = np.concatenate([station_feats, temporal_feats])
            X_tensor = torch.tensor(X, dtype=torch.float32).unsqueeze(0).to(DEVICE)

            # Get station and hour IDs
            station_id = torch.tensor([self.station_idx[station]]).to(DEVICE)
            hour_tensor = torch.tensor([hour]).to(DEVICE)

            # Predict
            with torch.no_grad():
                in_pred, out_pred = self.model.forward_station_flow(X_tensor, station_id, hour_tensor)
                return float(in_pred.cpu().item()), float(out_pred.cpu().item())

        except Exception as e:
            print(f"Error predicting station flow: {e}")
            return 0.0, 0.0

    def predict_od_flow(self, origin_station, dest_station, hour, surface_distance=None,
                       translate=None, time=None, wait_time=None):
        """
        Predict OD flow between two stations

        Args:
            origin_station (str): Origin station name
            dest_station (str): Destination station name
            hour (int): Hour
            surface_distance (float): Surface distance (optional, will use default if None)
            translate (float): Translation distance (optional)
            time (float): Travel time (optional)
            wait_time (float): Wait time (optional)

        Returns:
            float: Predicted OD flow
        """
        if origin_station not in self.station_idx or dest_station not in self.station_idx:
            print(f"Warning: Station not found in index")
            return 0.0

        try:
            # Get station features
            o_feats = self._get_station_features(origin_station)
            d_feats = self._get_station_features(dest_station)

            # Use default values if not provided
            if surface_distance is None:
                surface_distance = 10.0  # Default 10km
            if translate is None:
                translate = 5.0  # Default 5km
            if time is None:
                time = 30.0  # Default 30 minutes
            if wait_time is None:
                wait_time = 5.0  # Default 5 minutes

            # OD specific features
            od_feats = np.array([surface_distance, translate, time, wait_time], dtype=np.float32)

            # Normalize OD features
            if hasattr(self.od_scaler, 'scale_'):
                od_feats = self.od_scaler.transform(od_feats.reshape(1, -1)).flatten()

            # Temporal features
            temporal_feats = self._get_temporal_features(hour)

            # Interaction features
            o_d_distance = np.linalg.norm(o_feats - d_feats) if len(o_feats) == len(d_feats) else 0
            o_d_similarity = np.dot(o_feats, d_feats) / (np.linalg.norm(o_feats) * np.linalg.norm(d_feats) + 1e-8)
            interaction_feats = np.array([o_d_distance, o_d_similarity])

            # Combine all features
            X = np.concatenate([o_feats, d_feats, od_feats, temporal_feats, interaction_feats])
            X_tensor = torch.tensor(X, dtype=torch.float32).unsqueeze(0).to(DEVICE)

            # Get station and hour IDs
            o_station_id = torch.tensor([self.station_idx[origin_station]]).to(DEVICE)
            d_station_id = torch.tensor([self.station_idx[dest_station]]).to(DEVICE)
            hour_tensor = torch.tensor([hour]).to(DEVICE)

            # Predict
            with torch.no_grad():
                od_pred = self.model.forward_od_flow(X_tensor, o_station_id, d_station_id, hour_tensor)
                return float(od_pred.cpu().item())

        except Exception as e:
            print(f"Error predicting OD flow: {e}")
            return 0.0

    def predict_station_flows_batch(self, stations, hours):
        """
        Predict flows for multiple stations and hours

        Args:
            stations (list): List of station names
            hours (list): List of hours

        Returns:
            pandas.DataFrame: DataFrame with predictions
        """
        results = []

        for station in stations:
            for hour in hours:
                in_flow, out_flow = self.predict_station_flow(station, hour)
                results.append({
                    'station': station,
                    'hour': hour,
                    'predicted_inflow': in_flow,
                    'predicted_outflow': out_flow
                })

        return pd.DataFrame(results)

    def predict_od_flows_batch(self, od_pairs, hours):
        """
        Predict OD flows for multiple station pairs and hours

        Args:
            od_pairs (list): List of (origin, destination) tuples
            hours (list): List of hours

        Returns:
            pandas.DataFrame: DataFrame with predictions
        """
        results = []

        for origin, dest in od_pairs:
            for hour in hours:
                od_flow = self.predict_od_flow(origin, dest, hour)
                results.append({
                    'origin': origin,
                    'destination': dest,
                    'hour': hour,
                    'predicted_od_flow': od_flow
                })

        return pd.DataFrame(results)

    def get_available_stations(self):
        """Get list of available stations"""
        return list(self.stations)

    def save_predictions(self, predictions_df, filename):
        """Save predictions to CSV file"""
        output_path = os.path.join(DATA_DIR, filename)
        predictions_df.to_csv(output_path, index=False)
        print(f"Predictions saved to {output_path}")

# ------------------------------
# Example Usage Functions
# ------------------------------

def example_station_predictions():
    """Example: Predict station flows for specific stations and hours"""
    predictor = MetroFlowPredictor()

    # Example stations and hours
    stations = ['北京站', '西直门', '国贸']  # Replace with actual station names
    hours = [7, 8, 9, 17, 18, 19]  # Peak hours

    # Get predictions
    predictions = predictor.predict_station_flows_batch(stations, hours)
    print("\nStation Flow Predictions:")
    print(predictions)

    # Save results
    predictor.save_predictions(predictions, 'station_flow_predictions.csv')

    return predictions

def example_od_predictions():
    """Example: Predict OD flows for specific station pairs"""
    predictor = MetroFlowPredictor()

    # Example OD pairs
    od_pairs = [
        ('北京站', '西直门'),
        ('西直门', '国贸'),
        ('国贸', '北京站')
    ]  # Replace with actual station names
    hours = [8, 12, 18]  # Different times of day

    # Get predictions
    predictions = predictor.predict_od_flows_batch(od_pairs, hours)
    print("\nOD Flow Predictions:")
    print(predictions)

    # Save results
    predictor.save_predictions(predictions, 'od_flow_predictions.csv')

    return predictions

def example_single_predictions():
    """Example: Single predictions"""
    predictor = MetroFlowPredictor()

    # Single station flow prediction
    station = '北京站'  # Replace with actual station name
    hour = 8
    in_flow, out_flow = predictor.predict_station_flow(station, hour)
    print(f"\nSingle Station Prediction:")
    print(f"Station: {station}, Hour: {hour}")
    print(f"Predicted Inflow: {in_flow:.2f}")
    print(f"Predicted Outflow: {out_flow:.2f}")

    # Single OD flow prediction
    origin = '北京站'
    destination = '西直门'
    od_flow = predictor.predict_od_flow(origin, destination, hour)
    print(f"\nSingle OD Prediction:")
    print(f"Origin: {origin}, Destination: {destination}, Hour: {hour}")
    print(f"Predicted OD Flow: {od_flow:.2f}")

if __name__ == '__main__':
    print("Metro Flow Predictor - Inference Only")
    print("=====================================")

    try:
        # Show available stations
        predictor = MetroFlowPredictor()
        stations = predictor.get_available_stations()
        print(f"\nAvailable stations ({len(stations)}): {stations[:10]}...")  # Show first 10

        # Run examples
        print("\n" + "="*50)
        example_single_predictions()

        print("\n" + "="*50)
        example_station_predictions()

        print("\n" + "="*50)
        example_od_predictions()

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
