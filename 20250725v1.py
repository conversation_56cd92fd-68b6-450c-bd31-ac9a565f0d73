import os
import numpy as np
import pandas as pd
import geopandas as gpd
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score, mean_squared_error
import warnings
warnings.filterwarnings('ignore')

# ------------------------------
# Configuration and Hyperparameters
# ------------------------------
DATA_DIR = r"C:\Users\<USER>\Desktop\prev1"
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {DEVICE}")

# Enhanced training parameters
N_GRID = 70000    # approx number of grid nodes
N_STATION = 350   # approx number of station nodes
HIST_WINDOW = 6   # use past 6 hours for prediction
BATCH_SIZE = 64   # Reduced for complex model
EPOCHS = 100      # Increased for better convergence
LR = 2e-4         # Lower learning rate for stability
HIDDEN_DIM = 256  # Significantly increased for better representation
NUM_LAYERS = 3    # Increased depth
WEIGHT_DECAY = 1e-4  # L2 regularization
PATIENCE = 10     # Early stopping patience
MIN_DELTA = 1e-4  # Minimum improvement for early stopping

# Output files
OUT_IN = os.path.join(DATA_DIR, 'in_500_predictions_with_coords.shp')
OUT_OUT = os.path.join(DATA_DIR, 'out_500_predictions_with_coords.shp')
OUT_OD = os.path.join(DATA_DIR, 'od_predictions.csv')

# ------------------------------
# Utility functions
# ------------------------------
def load_shapefile(fname):
    return gpd.read_file(os.path.join(DATA_DIR, fname))

def build_station_graph(station_connect_csv):
    # Build adjacency for stations
    df = pd.read_csv(os.path.join(DATA_DIR, station_connect_csv))
    # clean names
    df['station_1'] = df['station_1'].str.replace(r'_.*$', '', regex=True)
    df['station_2'] = df['station_2'].str.replace(r'_.*$', '', regex=True)
    stations = pd.unique(df[['station_1','station_2']].values.ravel())
    idx = {s: i for i, s in enumerate(stations)}
    N = len(stations)
    A = np.zeros((N, N), dtype=np.float32)
    for _, row in df.iterrows():
        u, v = idx[row['station_1']], idx[row['station_2']]
        A[u, v] = A[v, u] = 1.0
    # Add self-loops
    np.fill_diagonal(A, 1.0)
    # normalize
    D = np.diag(1.0 / (A.sum(axis=1) + 1e-6))
    A_norm = D.dot(A)
    return torch.tensor(A_norm, device=DEVICE), stations, idx

def aggregate_station_features(grid_gdf, grid_feats_df, station_idx):
    """Aggregate grid features around each station within 1.5km radius"""
    station_feats = []

    for station_name in station_idx.keys():
        # Get grids associated with this station
        station_grids = grid_gdf[grid_gdf['station'] == station_name]

        if len(station_grids) > 0:
            # Get grid IDs for this station
            grid_ids = station_grids['grid_id'].values

            # Get features for these grids
            station_grid_feats = grid_feats_df[grid_feats_df['id'].isin(grid_ids)]

            if len(station_grid_feats) > 0:
                # Aggregate features (mean)
                numeric_cols = station_grid_feats.select_dtypes(include=[np.number]).columns
                numeric_cols = [col for col in numeric_cols if col != 'id']

                agg_feats = station_grid_feats[numeric_cols].mean()
                agg_feats['station'] = station_name
                station_feats.append(agg_feats)
            else:
                # Create zero features if no grid data
                numeric_cols = grid_feats_df.select_dtypes(include=[np.number]).columns
                numeric_cols = [col for col in numeric_cols if col != 'id']

                zero_feats = pd.Series(0.0, index=numeric_cols)
                zero_feats['station'] = station_name
                station_feats.append(zero_feats)
        else:
            # Create zero features if no grid data
            numeric_cols = grid_feats_df.select_dtypes(include=[np.number]).columns
            numeric_cols = [col for col in numeric_cols if col != 'id']

            zero_feats = pd.Series(0.0, index=numeric_cols)
            zero_feats['station'] = station_name
            station_feats.append(zero_feats)

    return pd.DataFrame(station_feats)

# ------------------------------
# Dataset definitions
# ------------------------------
class MetroDataset(Dataset):
    def __init__(self, in_data, out_data, od_df, grid_feats, station_feats, station_idx):
        """
        Combined dataset for Grid→Station flows and Station→Station OD flows
        """
        self.in_data = in_data.copy()
        self.out_data = out_data.copy()
        self.od_df = od_df.copy()
        self.grid_feats = grid_feats.copy()
        self.station_feats = station_feats.copy()
        self.station_idx = station_idx

        # Create station flow samples
        self.station_hourly_in = self.in_data.groupby(['hour', 'station'])['count'].sum().reset_index()
        self.station_hourly_out = self.out_data.groupby(['hour', 'station'])['count'].sum().reset_index()

        # Create OD samples from OD data
        self.od_samples = []
        if len(self.od_df) > 0:
            # Sample OD data for training (to manage memory)
            sample_size = min(50000, len(self.od_df))  # Limit to 50k samples
            od_sample = self.od_df.sample(n=sample_size, random_state=42)

            for _, row in od_sample.iterrows():
                if row['o_rawname'] in station_idx and row['d_rawname'] in station_idx:
                    self.od_samples.append({
                        'hour': row['hour'],
                        'o_station': row['o_rawname'],
                        'd_station': row['d_rawname'],
                        'trip': row['trip'],
                        'surface_distance': row['surface_distance'],
                        'translate': row['translate'],
                        'time': row['time'],
                        'wait_time': row['wait_time']
                    })

        # Create station flow samples (without temporal history)
        self.station_samples = []
        stations = list(station_idx.keys())
        hours = sorted(self.station_hourly_in['hour'].unique())

        for station in stations[:100]:  # Limit stations for memory
            for hour in hours:  # Use all hours, no history requirement
                target_in = self.station_hourly_in[
                    (self.station_hourly_in['hour'] == hour) &
                    (self.station_hourly_in['station'] == station)
                ]['count'].sum()

                target_out = self.station_hourly_out[
                    (self.station_hourly_out['hour'] == hour) &
                    (self.station_hourly_out['station'] == station)
                ]['count'].sum()

                self.station_samples.append({
                    'type': 'station_flow',
                    'station': station,
                    'hour': hour,
                    'target_in': target_in,
                    'target_out': target_out
                })

        # Combine all samples
        self.all_samples = self.station_samples + [
            {**sample, 'type': 'od_flow'} for sample in self.od_samples
        ]

        # Prepare enhanced scalers
        self.station_scaler = StandardScaler()
        self.od_scaler = StandardScaler()

        # Fit station features scaler
        numeric_cols = self.station_feats.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            self.station_scaler.fit(self.station_feats[numeric_cols])

        # Fit OD features scaler
        if len(self.od_samples) > 0:
            od_features = []
            for sample in self.od_samples:
                od_features.append([
                    sample['surface_distance'],
                    sample['translate'],
                    sample['time'],
                    sample['wait_time']
                ])
            self.od_scaler.fit(np.array(od_features))

    def __len__(self):
        return len(self.all_samples)

    def __getitem__(self, idx):
        sample = self.all_samples[idx]

        if sample['type'] == 'station_flow':
            return self._get_station_sample(sample)
        else:
            return self._get_od_sample(sample)

    def _get_station_sample(self, sample):
        station = sample['station']

        # Get enhanced station features
        station_feats = self.station_feats[self.station_feats['station'] == station]
        if len(station_feats) > 0:
            numeric_cols = station_feats.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 0:
                feats = station_feats[numeric_cols].values.flatten()
                feats = self.station_scaler.transform(feats.reshape(1, -1)).flatten()
            else:
                numeric_cols = self.station_feats.select_dtypes(include=[np.number]).columns
                feats = np.zeros(len(numeric_cols))
        else:
            numeric_cols = self.station_feats.select_dtypes(include=[np.number]).columns
            feats = np.zeros(len(numeric_cols))

        # Add temporal features (hour of day)
        hour_of_day = sample['hour'] % 24
        day_of_week = (sample['hour'] // 24) % 7
        is_weekend = 1.0 if day_of_week >= 5 else 0.0
        is_peak_hour = 1.0 if hour_of_day in [7, 8, 9, 17, 18, 19] else 0.0

        # Cyclical encoding for time
        hour_sin = np.sin(2 * np.pi * hour_of_day / 24)
        hour_cos = np.cos(2 * np.pi * hour_of_day / 24)
        day_sin = np.sin(2 * np.pi * day_of_week / 7)
        day_cos = np.cos(2 * np.pi * day_of_week / 7)

        temporal_feats = np.array([hour_sin, hour_cos, day_sin, day_cos, is_weekend, is_peak_hour])

        # Combine station features and temporal features (no historical flows)
        X = np.concatenate([feats, temporal_feats])

        return {
            'type': 'station_flow',
            'X': torch.tensor(X, dtype=torch.float32),
            'y_in': torch.tensor([sample['target_in']], dtype=torch.float32),
            'y_out': torch.tensor([sample['target_out']], dtype=torch.float32),
            'station_id': self.station_idx[station],
            'hour': sample['hour']
        }

    def _get_od_sample(self, sample):
        o_station = sample['o_station']
        d_station = sample['d_station']

        # Get enhanced origin and destination station features
        o_feats = self._get_station_features(o_station)
        d_feats = self._get_station_features(d_station)

        # Enhanced OD specific features with normalization
        od_feats = np.array([
            sample['surface_distance'],
            sample['translate'],
            sample['time'],
            sample['wait_time']
        ], dtype=np.float32)

        # Normalize OD features
        if hasattr(self.od_scaler, 'scale_'):
            od_feats = self.od_scaler.transform(od_feats.reshape(1, -1)).flatten()

        # Add temporal features
        hour_of_day = sample['hour'] % 24
        day_of_week = (sample['hour'] // 24) % 7
        is_weekend = 1.0 if day_of_week >= 5 else 0.0
        is_peak_hour = 1.0 if hour_of_day in [7, 8, 9, 17, 18, 19] else 0.0

        # Cyclical encoding for time
        hour_sin = np.sin(2 * np.pi * hour_of_day / 24)
        hour_cos = np.cos(2 * np.pi * hour_of_day / 24)
        day_sin = np.sin(2 * np.pi * day_of_week / 7)
        day_cos = np.cos(2 * np.pi * day_of_week / 7)

        temporal_feats = np.array([hour_sin, hour_cos, day_sin, day_cos, is_weekend, is_peak_hour])

        # Interaction features between origin and destination
        o_d_distance = np.linalg.norm(o_feats - d_feats) if len(o_feats) == len(d_feats) else 0
        o_d_similarity = np.dot(o_feats, d_feats) / (np.linalg.norm(o_feats) * np.linalg.norm(d_feats) + 1e-8)

        interaction_feats = np.array([o_d_distance, o_d_similarity])

        # Combine all features
        X = np.concatenate([o_feats, d_feats, od_feats, temporal_feats, interaction_feats])

        return {
            'type': 'od_flow',
            'X': torch.tensor(X, dtype=torch.float32),
            'y_od': torch.tensor([sample['trip']], dtype=torch.float32),
            'o_station_id': self.station_idx[o_station],
            'd_station_id': self.station_idx[d_station],
            'hour': sample['hour']
        }

    def _get_station_features(self, station):
        station_feats = self.station_feats[self.station_feats['station'] == station]
        if len(station_feats) > 0:
            numeric_cols = station_feats.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 0:
                feats = station_feats[numeric_cols].values.flatten()
                feats = self.station_scaler.transform(feats.reshape(1, -1)).flatten()
                return feats
        # Return zeros with the same dimension as the numeric columns
        numeric_cols = self.station_feats.select_dtypes(include=[np.number]).columns
        return np.zeros(len(numeric_cols))

# ------------------------------
# Advanced Multi-task Neural Network Model
# ------------------------------

class MultiHeadAttention(nn.Module):
    def __init__(self, d_model, num_heads, dropout=0.1):
        super().__init__()
        assert d_model % num_heads == 0

        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads

        self.W_q = nn.Linear(d_model, d_model)
        self.W_k = nn.Linear(d_model, d_model)
        self.W_v = nn.Linear(d_model, d_model)
        self.W_o = nn.Linear(d_model, d_model)

        self.dropout = nn.Dropout(dropout)

    def forward(self, query, key, value, mask=None):
        batch_size = query.size(0)

        # Linear transformations and split into heads
        Q = self.W_q(query).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        K = self.W_k(key).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        V = self.W_v(value).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)

        # Attention
        scores = torch.matmul(Q, K.transpose(-2, -1)) / np.sqrt(self.d_k)
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)

        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)

        context = torch.matmul(attention_weights, V)
        context = context.transpose(1, 2).contiguous().view(batch_size, -1, self.d_model)

        output = self.W_o(context)
        return output

class ResidualBlock(nn.Module):
    def __init__(self, input_dim, hidden_dim, dropout=0.2):
        super().__init__()
        self.linear1 = nn.Linear(input_dim, hidden_dim)
        self.linear2 = nn.Linear(hidden_dim, input_dim)
        self.norm1 = nn.LayerNorm(input_dim)
        self.norm2 = nn.LayerNorm(input_dim)
        self.dropout = nn.Dropout(dropout)
        self.activation = nn.GELU()

    def forward(self, x):
        residual = x
        x = self.norm1(x)
        x = self.linear1(x)
        x = self.activation(x)
        x = self.dropout(x)
        x = self.linear2(x)
        x = self.dropout(x)
        x = x + residual
        x = self.norm2(x)
        return x

# Removed TemporalEncoder as we're not using historical sequences

class AdvancedMetroFlowPredictor(nn.Module):
    def __init__(self, station_flow_input_dim, od_flow_input_dim, num_stations, num_grids=70000, num_hours=24, hidden_dim=256, embed_dim=64):
        super().__init__()
        self.hidden_dim = hidden_dim
        self.embed_dim = embed_dim
        self.num_stations = num_stations
        self.num_grids = num_grids
        self.num_hours = num_hours

        # Learnable embeddings for time, station, and geography
        self.hour_embedding = nn.Embedding(num_hours, embed_dim)  # 24 hours
        self.station_embedding = nn.Embedding(num_stations, embed_dim)  # Each station
        self.grid_embedding = nn.Embedding(num_grids, embed_dim)  # Each grid for geographic features

        # Station-specific hour embeddings (each station has its own hour pattern)
        self.station_hour_embedding = nn.Parameter(
            torch.randn(num_stations, num_hours, embed_dim) * 0.1
        )

        # Geographic region embeddings (divide city into regions)
        # We can create geographic regions based on station locations
        self.num_regions = min(50, num_stations // 8)  # Create geographic regions
        self.region_embedding = nn.Embedding(self.num_regions, embed_dim)

        # Station to region mapping (learnable)
        self.station_to_region = nn.Embedding(num_stations, self.num_regions)
        self.station_to_region.weight.data = F.softmax(
            torch.randn(num_stations, self.num_regions), dim=1
        )

        # Enhanced feature extractors with residual connections
        # Adjust input dimension to account for embeddings
        station_total_dim = station_flow_input_dim + embed_dim * 3  # station_feats + station_embed + hour_embed + region_embed
        self.station_feature_extractor = nn.Sequential(
            nn.Linear(station_total_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(0.3),
            ResidualBlock(hidden_dim, hidden_dim * 2),
            ResidualBlock(hidden_dim, hidden_dim * 2),
            ResidualBlock(hidden_dim, hidden_dim * 2)
        )

        # OD feature extractor with cross-attention
        # Adjust input dimension for OD features + embeddings
        od_total_dim = od_flow_input_dim + embed_dim * 6  # od_feats + 2*station_embed + 2*hour_embed + 2*region_embed
        self.od_feature_extractor = nn.Sequential(
            nn.Linear(od_total_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(0.3),
            ResidualBlock(hidden_dim, hidden_dim * 2),
            ResidualBlock(hidden_dim, hidden_dim * 2),
            ResidualBlock(hidden_dim, hidden_dim * 2)
        )

        # Cross-attention between origin and destination
        self.od_cross_attention = MultiHeadAttention(hidden_dim // 2, num_heads=8)

        # OD feature fusion layer
        self.od_fusion = nn.Sequential(
            nn.Linear(hidden_dim + hidden_dim // 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(0.2)
        )

        # Enhanced prediction heads with multiple scales
        self.in_predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.GELU(),
            nn.Linear(hidden_dim // 4, 1)
        )

        self.out_predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.GELU(),
            nn.Linear(hidden_dim // 4, 1)
        )

        self.od_predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.GELU(),
            nn.Linear(hidden_dim // 4, 1)
        )

    def forward_station_flow(self, x, station_ids, hours):
        """Enhanced forward pass for station flow prediction with embeddings"""
        # Get embeddings
        station_embeds = self.station_embedding(station_ids)  # (batch, embed_dim)
        hour_embeds = self.hour_embedding(hours % 24)  # (batch, embed_dim)

        # Get station-specific hour embeddings
        station_hour_embeds = self.station_hour_embedding[station_ids, hours % 24]  # (batch, embed_dim)

        # Get geographic region embeddings
        station_region_weights = self.station_to_region(station_ids)  # (batch, num_regions)
        region_embeds = torch.matmul(station_region_weights, self.region_embedding.weight)  # (batch, embed_dim)

        # Combine all features
        combined_features = torch.cat([
            x,
            station_embeds,
            hour_embeds + station_hour_embeds,  # Add station-specific hour pattern
            region_embeds  # Add geographic region information
        ], dim=1)

        # Extract features
        features = self.station_feature_extractor(combined_features)

        # Predictions
        in_pred = self.in_predictor(features)
        out_pred = self.out_predictor(features)

        return in_pred.squeeze(-1), out_pred.squeeze(-1)

    def forward_od_flow(self, x, o_station_ids, d_station_ids, hours):
        """Enhanced forward pass for OD flow prediction with embeddings"""
        # Get embeddings for origin and destination
        o_station_embeds = self.station_embedding(o_station_ids)
        d_station_embeds = self.station_embedding(d_station_ids)
        o_hour_embeds = self.hour_embedding(hours % 24)
        d_hour_embeds = self.hour_embedding(hours % 24)  # Same hour for both

        # Get station-specific hour embeddings
        o_station_hour_embeds = self.station_hour_embedding[o_station_ids, hours % 24]
        d_station_hour_embeds = self.station_hour_embedding[d_station_ids, hours % 24]

        # Get geographic region embeddings for origin and destination
        o_region_weights = self.station_to_region(o_station_ids)
        d_region_weights = self.station_to_region(d_station_ids)
        o_region_embeds = torch.matmul(o_region_weights, self.region_embedding.weight)
        d_region_embeds = torch.matmul(d_region_weights, self.region_embedding.weight)

        # Combine all features
        combined_features = torch.cat([
            x,
            o_station_embeds,
            d_station_embeds,
            o_hour_embeds + o_station_hour_embeds,
            d_hour_embeds + d_station_hour_embeds,
            o_region_embeds,
            d_region_embeds
        ], dim=1)

        # Extract features
        features = self.od_feature_extractor(combined_features)

        # Split into origin and destination features for cross-attention
        mid_point = features.size(1) // 2
        origin_features = features[:, :mid_point].unsqueeze(1)
        dest_features = features[:, mid_point:].unsqueeze(1)

        # Cross-attention between origin and destination
        attended_features = self.od_cross_attention(origin_features, dest_features, dest_features)
        attended_features = attended_features.squeeze(1)

        # Combine with original features using the fusion layer
        combined_features = torch.cat([features, attended_features], dim=1)
        fused_features = self.od_fusion(combined_features)

        # Prediction
        od_pred = self.od_predictor(fused_features)
        return od_pred.squeeze(-1)

# ------------------------------
# Training and Inference
# ------------------------------
def train():
    print("Loading data...")

    # Load data files
    try:
        # Load CSV data (grid-station flows)
        in_data = pd.read_csv(os.path.join(DATA_DIR, 'in_500.csv'))
        out_data = pd.read_csv(os.path.join(DATA_DIR, 'out_500.csv'))
        grid_feats = pd.read_csv(os.path.join(DATA_DIR, 'leti_data.csv'))

        print(f"Loaded in_data: {len(in_data)} records")
        print(f"Loaded out_data: {len(out_data)} records")
        print(f"Loaded grid_feats: {len(grid_feats)} records")

        # Load OD data
        od_df = pd.read_csv(os.path.join(DATA_DIR, 'updated北京市_subway_od_2024_modified3_deduplicated.csv'))
        print(f"Loaded OD data: {len(od_df)} records")

        # Build station graph
        A_station, stations, station_idx = build_station_graph('station_connect_2023.csv')
        print(f"Built station graph with {len(stations)} stations")

        # Try to use 1.5km radius aggregation if spatial data is available
        try:
            # Load spatial data for 1.5km radius aggregation
            in_gdf = load_shapefile('in_500_with_coords.shp')
            print(f"Loaded spatial data with {len(in_gdf)} records")

            # Create grid_gdf with station associations
            # Assuming the shapefile has grid_id and station columns
            if 'grid_id' in in_gdf.columns and 'station' in in_gdf.columns:
                grid_gdf = in_gdf[['grid_id', 'station']].drop_duplicates()
                print(f"Using 1.5km radius feature aggregation with {len(grid_gdf)} grid-station associations")
                station_feats = aggregate_station_features(grid_gdf, grid_feats, station_idx)
            else:
                print("Spatial data missing required columns, falling back to simple aggregation")
                raise ValueError("Missing required columns")

        except Exception as e:
            print(f"Could not use 1.5km radius aggregation: {e}")
            print("Falling back to simple aggregation method")

            # Fallback to simple aggregation
            station_grid_map = {}
            for station in stations:
                station_grids = in_data[in_data['station'] == station]['grid_id'].unique()
                station_grid_map[station] = station_grids

            station_feats = aggregate_station_features_simple(grid_feats, station_grid_map, stations)

        print(f"Aggregated station features: {station_feats.shape}")

        # Prepare dataset and loader
        dataset = MetroDataset(in_data, out_data, od_df, grid_feats, station_feats, station_idx)

        # Custom collate function to handle mixed batch types
        def collate_fn(batch):
            station_flow_batch = []
            od_flow_batch = []

            for item in batch:
                if item['type'] == 'station_flow':
                    station_flow_batch.append(item)
                else:
                    od_flow_batch.append(item)

            return {'station_flow': station_flow_batch, 'od_flow': od_flow_batch}

        loader = DataLoader(dataset, batch_size=BATCH_SIZE, shuffle=True, collate_fn=collate_fn)
        print(f"Created dataset with {len(dataset)} samples")

        # Determine input dimensions dynamically (without historical flows)
        # Station flow input: station features + temporal features (6)
        station_feature_dim = len(station_feats.select_dtypes(include=[np.number]).columns)
        station_input_dim = station_feature_dim + 6  # station_feats + temporal

        # OD flow input: origin features + destination features + OD features (4) + temporal features (6) + interaction features (2)
        od_input_dim = station_feature_dim * 2 + 4 + 6 + 2  # origin + destination + od_feats + temporal + interaction

        print(f"Station feature dimension: {station_feature_dim}")
        print(f"Station input dimension: {station_input_dim}")
        print(f"OD input dimension: {od_input_dim}")

        # Verify with actual samples
        sample_station = None
        sample_od = None
        for i in range(min(100, len(dataset))):
            sample = dataset[i]
            if sample['type'] == 'station_flow' and sample_station is None:
                sample_station = sample
            elif sample['type'] == 'od_flow' and sample_od is None:
                sample_od = sample
            if sample_station is not None and sample_od is not None:
                break

        if sample_station:
            actual_station_dim = sample_station['X'].shape[0]
            print(f"Actual station sample dimension: {actual_station_dim}")
            if actual_station_dim != station_input_dim:
                print(f"Warning: Dimension mismatch for station input. Expected {station_input_dim}, got {actual_station_dim}")
                station_input_dim = actual_station_dim

        if sample_od:
            actual_od_dim = sample_od['X'].shape[0]
            print(f"Actual OD sample dimension: {actual_od_dim}")
            if actual_od_dim != od_input_dim:
                print(f"Warning: Dimension mismatch for OD input. Expected {od_input_dim}, got {actual_od_dim}")
                od_input_dim = actual_od_dim

        print(f"Station input dimension: {station_input_dim}")
        print(f"OD input dimension: {od_input_dim}")

        # Model
        model = AdvancedMetroFlowPredictor(
            station_flow_input_dim=station_input_dim,
            od_flow_input_dim=od_input_dim,
            num_stations=len(stations),
            hidden_dim=HIDDEN_DIM
        )
        model.to(DEVICE)

        # Enhanced optimizer with weight decay
        optimizer = optim.AdamW(model.parameters(), lr=LR, weight_decay=WEIGHT_DECAY)

        # Learning rate scheduler
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.5, patience=5
        )

        # Loss functions with different weights
        mse_criterion = nn.MSELoss()
        huber_criterion = nn.HuberLoss(delta=1.0)  # More robust to outliers

        print("Starting enhanced training...")

        # Enhanced training loop with early stopping
        best_loss = float('inf')
        patience_counter = 0
        training_history = []

        for epoch in range(EPOCHS):
            model.train()
            total_loss = 0
            num_batches = 0

            for batch_data in loader:
                optimizer.zero_grad()
                batch_loss = 0

                # Process station flow samples
                if batch_data['station_flow']:
                    station_X = torch.stack([item['X'] for item in batch_data['station_flow']]).to(DEVICE)
                    station_y_in = torch.stack([item['y_in'] for item in batch_data['station_flow']]).to(DEVICE)
                    station_y_out = torch.stack([item['y_out'] for item in batch_data['station_flow']]).to(DEVICE)
                    station_ids = torch.tensor([item['station_id'] for item in batch_data['station_flow']]).to(DEVICE)
                    hours = torch.tensor([item['hour'] for item in batch_data['station_flow']]).to(DEVICE)

                    pred_in, pred_out = model.forward_station_flow(station_X, station_ids, hours)

                    # Use Huber loss for better robustness
                    loss_in = huber_criterion(pred_in, station_y_in.squeeze())
                    loss_out = huber_criterion(pred_out, station_y_out.squeeze())
                    batch_loss += loss_in + loss_out

                # Process OD flow samples
                if batch_data['od_flow']:
                    od_X = torch.stack([item['X'] for item in batch_data['od_flow']]).to(DEVICE)
                    od_y = torch.stack([item['y_od'] for item in batch_data['od_flow']]).to(DEVICE)
                    o_station_ids = torch.tensor([item['o_station_id'] for item in batch_data['od_flow']]).to(DEVICE)
                    d_station_ids = torch.tensor([item['d_station_id'] for item in batch_data['od_flow']]).to(DEVICE)
                    hours = torch.tensor([item['hour'] for item in batch_data['od_flow']]).to(DEVICE)

                    pred_od = model.forward_od_flow(od_X, o_station_ids, d_station_ids, hours)
                    # Weight OD loss higher and use Huber loss
                    loss_od = huber_criterion(pred_od, od_y.squeeze())
                    batch_loss += loss_od * 3  # Increased weight for OD prediction

                if batch_loss > 0:
                    batch_loss.backward()
                    optimizer.step()
                    total_loss += batch_loss.item()
                    num_batches += 1

            if num_batches > 0:
                avg_loss = total_loss / num_batches
                training_history.append(avg_loss)

                # Learning rate scheduling
                scheduler.step(avg_loss)
                current_lr = optimizer.param_groups[0]['lr']

                print(f"Epoch {epoch+1}/{EPOCHS}, Loss: {avg_loss:.4f}, LR: {current_lr:.6f}")

                # Early stopping logic
                if avg_loss < best_loss - MIN_DELTA:
                    best_loss = avg_loss
                    patience_counter = 0
                    torch.save(model.state_dict(), os.path.join(DATA_DIR, 'best_advanced_metro_model.pth'))
                    print(f"New best model saved with loss: {best_loss:.4f}")
                else:
                    patience_counter += 1

                if patience_counter >= PATIENCE:
                    print(f"Early stopping triggered after {epoch+1} epochs")
                    break

        print("Training completed!")

        # Generate predictions
        print("Generating predictions...")
        generate_predictions_v2(model, dataset, in_data, out_data, od_df, station_idx)

        # Generate full OD predictions as per documentation
        print("Generating full OD predictions...")
        generate_full_od_predictions(model, od_df, dataset, station_idx)

    except Exception as e:
        print(f"Error during training: {e}")
        import traceback
        traceback.print_exc()

def aggregate_station_features_simple(grid_feats, station_grid_map, stations):
    """Simple aggregation of station features"""
    station_feats_list = []

    # Get numeric columns
    numeric_cols = grid_feats.select_dtypes(include=[np.number]).columns
    numeric_cols = [col for col in numeric_cols if col != 'id']

    for station in stations:
        if station in station_grid_map and len(station_grid_map[station]) > 0:
            # Get features for grids associated with this station
            station_grid_feats = grid_feats[grid_feats['id'].isin(station_grid_map[station])]

            if len(station_grid_feats) > 0:
                # Aggregate features (mean)
                agg_feats = station_grid_feats[numeric_cols].mean()
            else:
                # Create zero features
                agg_feats = pd.Series(0.0, index=numeric_cols)
        else:
            # Create zero features
            agg_feats = pd.Series(0.0, index=numeric_cols)

        agg_feats['station'] = station
        station_feats_list.append(agg_feats)

    return pd.DataFrame(station_feats_list)

def generate_predictions_v2(model, dataset, in_data, out_data, od_df, station_idx):
    """Generate predictions for all three tasks and save to files"""
    try:
        model.eval()

        # Separate predictions by type
        station_predictions_in = []
        station_predictions_out = []
        od_predictions = []

        station_info = []
        od_info = []

        print("Generating predictions for all samples...")

        with torch.no_grad():
            for i in range(len(dataset)):
                sample = dataset[i]

                if sample['type'] == 'station_flow':
                    X = sample['X'].unsqueeze(0).to(DEVICE)
                    station_id = torch.tensor([sample['station_id']]).to(DEVICE)
                    hour = torch.tensor([sample['hour']]).to(DEVICE)
                    pred_in, pred_out = model.forward_station_flow(X, station_id, hour)

                    # Get station name from station_id
                    station_name = None
                    for name, sid in station_idx.items():
                        if sid == sample['station_id']:
                            station_name = name
                            break

                    if station_name:
                        station_predictions_in.append(pred_in.cpu().item())
                        station_predictions_out.append(pred_out.cpu().item())

                        # Find corresponding sample info
                        for s in dataset.station_samples:
                            if (s['station'] == station_name and
                                dataset.station_idx[s['station']] == sample['station_id']):
                                station_info.append({
                                    'station': station_name,
                                    'hour': s['hour'],
                                    'true_in': s['target_in'],
                                    'true_out': s['target_out']
                                })
                                break

                elif sample['type'] == 'od_flow':
                    X = sample['X'].unsqueeze(0).to(DEVICE)
                    o_station_id = torch.tensor([sample['o_station_id']]).to(DEVICE)
                    d_station_id = torch.tensor([sample['d_station_id']]).to(DEVICE)
                    hour = torch.tensor([sample['hour']]).to(DEVICE)
                    pred_od = model.forward_od_flow(X, o_station_id, d_station_id, hour)

                    # Get station names
                    o_station = None
                    d_station = None
                    for name, sid in station_idx.items():
                        if sid == sample['o_station_id']:
                            o_station = name
                        if sid == sample['d_station_id']:
                            d_station = name

                    if o_station and d_station:
                        od_predictions.append(pred_od.cpu().item())

                        # Find corresponding OD sample info
                        for s in dataset.od_samples:
                            if (s['o_station'] == o_station and s['d_station'] == d_station):
                                od_info.append({
                                    'o_station': o_station,
                                    'd_station': d_station,
                                    'hour': s['hour'],
                                    'true_trip': s['trip'],
                                    'surface_distance': s['surface_distance'],
                                    'translate': s['translate'],
                                    'time': s['time'],
                                    'wait_time': s['wait_time']
                                })
                                break

        # Create prediction DataFrames
        if station_info and station_predictions_in:
            pred_df_in = pd.DataFrame({
                'station': [info['station'] for info in station_info],
                'hour': [info['hour'] for info in station_info],
                'prediction': station_predictions_in,
                'true_value': [info['true_in'] for info in station_info]
            })

            pred_df_out = pd.DataFrame({
                'station': [info['station'] for info in station_info],
                'hour': [info['hour'] for info in station_info],
                'prediction': station_predictions_out,
                'true_value': [info['true_out'] for info in station_info]
            })

            # Save station flow predictions
            pred_df_in.to_csv(os.path.join(DATA_DIR, 'in_flow_predictions_v2.csv'), index=False)
            pred_df_out.to_csv(os.path.join(DATA_DIR, 'out_flow_predictions_v2.csv'), index=False)

            print(f"Saved station predictions: {len(pred_df_in)} in-flow, {len(pred_df_out)} out-flow")

        if od_info and od_predictions:
            pred_df_od = pd.DataFrame({
                'o_station': [info['o_station'] for info in od_info],
                'd_station': [info['d_station'] for info in od_info],
                'hour': [info['hour'] for info in od_info],
                'prediction': od_predictions,
                'true_value': [info['true_trip'] for info in od_info],
                'surface_distance': [info['surface_distance'] for info in od_info],
                'translate': [info['translate'] for info in od_info],
                'time': [info['time'] for info in od_info],
                'wait_time': [info['wait_time'] for info in od_info]
            })

            # Save OD predictions
            pred_df_od.to_csv(OUT_OD, index=False)
            print(f"Saved OD predictions: {len(pred_df_od)} records")

        # Calculate R² scores
        metrics = {}

        if station_info and station_predictions_in:
            true_in = [info['true_in'] for info in station_info]
            true_out = [info['true_out'] for info in station_info]

            r2_in = r2_score(true_in, station_predictions_in)
            r2_out = r2_score(true_out, station_predictions_out)

            metrics.update({
                'r2_in': r2_in,
                'r2_out': r2_out,
                'mse_in': mean_squared_error(true_in, station_predictions_in),
                'mse_out': mean_squared_error(true_out, station_predictions_out)
            })

            print(f"R² Score - In Flow: {r2_in:.4f}")
            print(f"R² Score - Out Flow: {r2_out:.4f}")

        if od_info and od_predictions:
            true_od = [info['true_trip'] for info in od_info]
            r2_od = r2_score(true_od, od_predictions)

            metrics.update({
                'r2_od': r2_od,
                'mse_od': mean_squared_error(true_od, od_predictions)
            })

            print(f"R² Score - OD Flow: {r2_od:.4f}")

        # Save performance metrics
        if metrics:
            import json
            with open(os.path.join(DATA_DIR, 'model_metrics_v2.json'), 'w') as f:
                json.dump(metrics, f, indent=2)

        # Try to create shapefile outputs
        try:
            if station_info and station_predictions_in:
                create_shapefile_outputs_v2(pred_df_in, pred_df_out)
        except Exception as e:
            print(f"Could not create shapefile outputs: {e}")

    except Exception as e:
        print(f"Error generating predictions: {e}")
        import traceback
        traceback.print_exc()

def generate_full_od_predictions(model, od_df, dataset, station_idx):
    """Generate predictions for all OD pairs in the full dataset"""
    try:
        model.eval()
        print(f"Processing {len(od_df)} OD records...")

        # Prepare predictions list
        full_od_predictions = []

        # Process in batches to avoid memory issues
        batch_size = 1000

        with torch.no_grad():
            for i in range(0, len(od_df), batch_size):
                batch_end = min(i + batch_size, len(od_df))
                batch_df = od_df.iloc[i:batch_end]

                batch_predictions = []

                for _, row in batch_df.iterrows():
                    # Check if stations exist in our station index
                    if row['o_rawname'] in station_idx and row['d_rawname'] in station_idx:
                        # Get station features
                        o_feats = dataset._get_station_features(row['o_rawname'])
                        d_feats = dataset._get_station_features(row['d_rawname'])

                        # Enhanced OD specific features with normalization
                        od_feats = np.array([
                            row['surface_distance'],
                            row['translate'],
                            row['time'],
                            row['wait_time']
                        ], dtype=np.float32)

                        # Normalize OD features
                        if hasattr(dataset.od_scaler, 'scale_'):
                            od_feats = dataset.od_scaler.transform(od_feats.reshape(1, -1)).flatten()

                        # Add temporal features
                        hour_of_day = row['hour'] % 24
                        day_of_week = (row['hour'] // 24) % 7
                        is_weekend = 1.0 if day_of_week >= 5 else 0.0
                        is_peak_hour = 1.0 if hour_of_day in [7, 8, 9, 17, 18, 19] else 0.0

                        # Cyclical encoding for time
                        hour_sin = np.sin(2 * np.pi * hour_of_day / 24)
                        hour_cos = np.cos(2 * np.pi * hour_of_day / 24)
                        day_sin = np.sin(2 * np.pi * day_of_week / 7)
                        day_cos = np.cos(2 * np.pi * day_of_week / 7)

                        temporal_feats = np.array([hour_sin, hour_cos, day_sin, day_cos, is_weekend, is_peak_hour])

                        # Interaction features between origin and destination
                        o_d_distance = np.linalg.norm(o_feats - d_feats) if len(o_feats) == len(d_feats) else 0
                        o_d_similarity = np.dot(o_feats, d_feats) / (np.linalg.norm(o_feats) * np.linalg.norm(d_feats) + 1e-8)

                        interaction_feats = np.array([o_d_distance, o_d_similarity])

                        # Combine all features
                        X = np.concatenate([o_feats, d_feats, od_feats, temporal_feats, interaction_feats])
                        X_tensor = torch.tensor(X, dtype=torch.float32).unsqueeze(0).to(DEVICE)

                        # Predict
                        o_station_id = torch.tensor([station_idx[row['o_rawname']]]).to(DEVICE)
                        d_station_id = torch.tensor([station_idx[row['d_rawname']]]).to(DEVICE)
                        hour = torch.tensor([row['hour']]).to(DEVICE)
                        pred_od = model.forward_od_flow(X_tensor, o_station_id, d_station_id, hour)
                        batch_predictions.append(pred_od.cpu().item())
                    else:
                        # If stations not found, use 0 as prediction
                        batch_predictions.append(0.0)

                full_od_predictions.extend(batch_predictions)

                if (i // batch_size + 1) % 10 == 0:
                    print(f"Processed {i + len(batch_predictions)} / {len(od_df)} records")

        # Add predictions to the dataframe
        od_df_with_predictions = od_df.copy()
        od_df_with_predictions['prediction'] = full_od_predictions

        # Save to CSV
        od_df_with_predictions.to_csv(OUT_OD, index=False)
        print(f"Saved full OD predictions to {OUT_OD}")

        # Calculate metrics if we have true values
        if 'trip' in od_df.columns:
            true_values = od_df['trip'].values
            pred_values = np.array(full_od_predictions)

            # Filter out zero predictions for metrics calculation
            valid_mask = pred_values > 0
            if np.sum(valid_mask) > 0:
                true_valid = true_values[valid_mask]
                pred_valid = pred_values[valid_mask]

                r2 = r2_score(true_valid, pred_valid)
                mse = mean_squared_error(true_valid, pred_valid)

                print(f"Full OD Prediction Metrics:")
                print(f"R² Score: {r2:.4f}")
                print(f"MSE: {mse:.4f}")
                print(f"Valid predictions: {np.sum(valid_mask)} / {len(pred_values)}")

    except Exception as e:
        print(f"Error generating full OD predictions: {e}")
        import traceback
        traceback.print_exc()

def create_shapefile_outputs(pred_df_in, pred_df_out, in_data, out_data):
    """Create shapefile outputs with predictions"""
    try:
        # Load existing shapefiles if they exist
        try:
            in_gdf = load_shapefile('in_500_with_coords.shp')
            out_gdf = load_shapefile('out_500_with_coords.shp')

            # Merge predictions with spatial data
            # This is a simplified approach - in practice you'd need proper spatial joining
            in_gdf['prediction'] = 0.0  # Default value
            out_gdf['prediction'] = 0.0  # Default value

            # Save updated shapefiles
            in_gdf.to_file(OUT_IN)
            out_gdf.to_file(OUT_OUT)

            print("Created shapefile outputs with predictions")

        except Exception as e:
            print(f"Could not load/process shapefiles: {e}")

    except Exception as e:
        print(f"Error creating shapefile outputs: {e}")

def create_shapefile_outputs_v2(pred_df_in, pred_df_out):
    """Create shapefile outputs with predictions - improved version"""
    try:
        # Load existing shapefiles if they exist
        try:
            in_gdf = load_shapefile('in_500_with_coords.shp')
            out_gdf = load_shapefile('out_500_with_coords.shp')

            print(f"Loaded in_gdf with {len(in_gdf)} records")
            print(f"Loaded out_gdf with {len(out_gdf)} records")
            print(f"Prediction data: in={len(pred_df_in)}, out={len(pred_df_out)}")

            # Initialize prediction columns with zeros
            in_gdf['prediction'] = 0.0
            out_gdf['prediction'] = 0.0

            # Try to merge predictions with spatial data based on station names
            if len(pred_df_in) > 0 and 'station' in in_gdf.columns:
                # Group predictions by station and take mean
                station_pred_in = pred_df_in.groupby('station')['prediction'].mean().reset_index()

                # Merge with shapefile data
                for _, row in station_pred_in.iterrows():
                    mask = in_gdf['station'] == row['station']
                    if mask.any():
                        in_gdf.loc[mask, 'prediction'] = row['prediction']

                print(f"Updated {len(station_pred_in)} stations with in-flow predictions")

            if len(pred_df_out) > 0 and 'station' in out_gdf.columns:
                # Group predictions by station and take mean
                station_pred_out = pred_df_out.groupby('station')['prediction'].mean().reset_index()

                # Merge with shapefile data
                for _, row in station_pred_out.iterrows():
                    mask = out_gdf['station'] == row['station']
                    if mask.any():
                        out_gdf.loc[mask, 'prediction'] = row['prediction']

                print(f"Updated {len(station_pred_out)} stations with out-flow predictions")

            # Save updated shapefiles
            in_gdf.to_file(OUT_IN)
            out_gdf.to_file(OUT_OUT)

            print(f"Created shapefile outputs:")
            print(f"  - {OUT_IN}")
            print(f"  - {OUT_OUT}")

        except Exception as e:
            print(f"Could not load/process shapefiles: {e}")
            import traceback
            traceback.print_exc()

    except Exception as e:
        print(f"Error creating shapefile outputs: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    print("Starting Metro Flow Prediction System...")
    train()

