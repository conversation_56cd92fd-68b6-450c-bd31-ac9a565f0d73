# Advanced Metro Flow Prediction System

## 概述 (Overview)

本项目实现了一个基于深度学习的地铁客流预测系统，能够同时预测站点进出客流和站点间OD（Origin-Destination）客流。该系统采用了先进的神经网络架构，结合了多头注意力机制、残差连接和多种嵌入技术。

This project implements an advanced deep learning-based metro flow prediction system that can simultaneously predict station inflow/outflow and Origin-Destination (OD) flows between stations. The system uses advanced neural network architectures with multi-head attention mechanisms, residual connections, and various embedding techniques.

## 核心算法架构 (Core Algorithm Architecture)

### 1. 数据处理流水线 (Data Processing Pipeline)

#### 输入数据类型 (Input Data Types)
- **网格-站点流量数据**: `in_500.csv`, `out_500.csv` - 网格到站点的进出客流
- **OD流量数据**: `updated北京市_subway_od_2024_modified3_deduplicated.csv` - 站点间客流
- **网格特征数据**: `leti_data.csv` - 地理网格的社会经济特征
- **站点连接数据**: `station_connect_2023.csv` - 地铁站点网络拓扑

#### 特征工程 (Feature Engineering)
1. **时间特征编码**:
   - 循环编码: `sin/cos(2π * hour/24)`, `sin/cos(2π * day/7)`
   - 离散特征: 是否周末、是否高峰时段
   
2. **空间特征聚合**:
   - 将网格特征按站点聚合（1.5km半径内）
   - 计算站点间距离和相似度特征

3. **网络拓扑特征**:
   - 构建站点邻接矩阵并进行归一化
   - 添加自环连接

### 2. 神经网络模型架构 (Neural Network Architecture)

#### AdvancedMetroFlowPredictor 主模型

**核心组件**:

1. **多层嵌入系统 (Multi-layer Embedding System)**:
   ```python
   - 时间嵌入: hour_embedding (24小时)
   - 站点嵌入: station_embedding (每个站点)
   - 网格嵌入: grid_embedding (地理网格)
   - 站点-时间嵌入: station_hour_embedding (站点特定时间模式)
   - 地理区域嵌入: region_embedding (地理区域划分)
   ```

2. **多头注意力机制 (Multi-Head Attention)**:
   ```python
   class MultiHeadAttention:
   - 8个注意力头
   - 查询-键-值变换
   - 缩放点积注意力
   - Dropout正则化
   ```

3. **残差块 (Residual Blocks)**:
   ```python
   class ResidualBlock:
   - 层归一化 (LayerNorm)
   - GELU激活函数
   - Dropout正则化
   - 跳跃连接
   ```

#### 双任务预测架构 (Dual-Task Prediction Architecture)

**任务1: 站点客流预测**
- 输入维度: 站点特征 + 时间特征 + 嵌入特征
- 输出: 进站客流 + 出站客流

**任务2: OD客流预测**
- 输入维度: 起点特征 + 终点特征 + OD特征 + 时间特征 + 交互特征
- 交叉注意力: 起点-终点特征交互
- 输出: OD客流量

### 3. 训练策略 (Training Strategy)

#### 超参数配置
```python
BATCH_SIZE = 64
EPOCHS = 50
LEARNING_RATE = 2e-4
HIDDEN_DIM = 256
NUM_LAYERS = 3
WEIGHT_DECAY = 1e-4
```

#### 优化策略
1. **优化器**: SGD (临时替代AdamW以解决兼容性问题)
2. **学习率调度**: ReduceLROnPlateau (验证损失平台期时降低学习率)
3. **损失函数**: 
   - MSE Loss (均方误差)
   - Huber Loss (对异常值更鲁棒)
4. **早停机制**: 10个epoch的耐心值

#### 正则化技术
- L2权重衰减
- Dropout (0.1-0.3)
- 层归一化
- 梯度裁剪

### 4. 数据集设计 (Dataset Design)

#### MetroDataset 类
- **混合批次处理**: 同时处理站点流量和OD流量样本
- **特征标准化**: 使用StandardScaler对数值特征进行标准化
- **动态采样**: OD数据限制为50,000样本以管理内存

#### 自定义批次整理器 (Custom Collate Function)
```python
def collate_fn(batch):
    # 分离站点流量和OD流量样本
    # 支持混合批次训练
```

## 技术特点 (Technical Features)

### 1. 先进的注意力机制
- 多头自注意力用于特征交互
- 起点-终点交叉注意力用于OD预测
- 缩放点积注意力计算

### 2. 多尺度特征融合
- 时间尺度: 小时、天、周
- 空间尺度: 网格、站点、区域
- 语义尺度: 社会经济特征

### 3. 端到端学习
- 联合训练站点流量和OD流量预测
- 共享特征表示学习
- 多任务损失优化

### 4. 鲁棒性设计
- 处理缺失数据
- 异常值检测和处理
- 模型正则化

## 文件结构 (File Structure)

```
├── 20250725v1.py           # 主算法实现
├── in_500.csv              # 进站流量数据
├── out_500.csv             # 出站流量数据
├── leti_data.csv           # 网格特征数据
├── station_connect_2023.csv # 站点连接数据
├── updated北京市_subway_od_2024_modified3_deduplicated.csv # OD流量数据
└── README.md               # 本文档
```

## 输出结果 (Output Results)

1. **预测文件**:
   - `in_500_predictions_with_coords.shp` - 进站流量预测(Shapefile)
   - `out_500_predictions_with_coords.shp` - 出站流量预测(Shapefile)
   - `od_predictions.csv` - OD流量预测

2. **模型文件**:
   - `best_advanced_metro_model.pth` - 最佳模型权重

3. **评估指标**:
   - `model_metrics_v2.json` - R²分数、MSE等评估指标

## 性能指标 (Performance Metrics)

- **R² Score**: 模型解释方差比例
- **MSE**: 均方误差
- **分任务评估**: 站点流量和OD流量分别评估

## 使用方法 (Usage)

```bash
# 运行训练和预测
python 20250725v1.py
```

## 依赖环境 (Dependencies)

```python
torch >= 1.9.0
pandas >= 1.3.0
numpy >= 1.21.0
geopandas >= 0.9.0
scikit-learn >= 1.0.0
```

## 注意事项 (Notes)

1. **内存管理**: OD数据采样限制为50,000条以控制内存使用
2. **GPU支持**: 自动检测CUDA设备，支持GPU加速训练
3. **兼容性**: 当前使用SGD优化器以避免NetworkX兼容性问题

## 算法创新点 (Algorithm Innovations)

1. **多任务联合学习**: 同时优化站点流量和OD流量预测
2. **层次化嵌入**: 时间-站点-区域多层次特征表示
3. **交叉注意力**: 起点-终点特征交互建模
4. **残差连接**: 深层网络训练稳定性
5. **自适应学习率**: 基于验证损失的动态调整

该系统代表了地铁客流预测领域的先进技术水平，结合了深度学习、注意力机制和多任务学习的最新进展。
