# Shapefile 转 CSV 工具使用说明

## 概述

我为您创建了两个脚本来将目录中的shp文件转换为csv文件：

1. **`quick_shp_to_csv.py`** - 快速转换脚本（推荐）
2. **`shp_to_csv_converter.py`** - 完整功能脚本

## 🚀 快速使用（推荐）

### 使用 quick_shp_to_csv.py

这是最简单的方式，直接转换当前目录中的所有shp文件：

```bash
python quick_shp_to_csv.py
```

**特点：**
- ✅ 自动扫描当前目录的所有.shp文件
- ✅ 自动转换为同名的.csv文件
- ✅ 保留所有属性数据
- ✅ 自动处理几何信息：
  - 点几何：提取经纬度坐标
  - 面几何：计算质心坐标
  - 所有几何：保存WKT格式
- ✅ 显示详细的转换进度和结果

## 🔧 完整功能使用

### 使用 shp_to_csv_converter.py

这个脚本提供更多选项和交互式界面：

```bash
python shp_to_csv_converter.py
```

**功能特点：**
- 📁 可以指定任意目录
- 📁 可以指定输出目录
- ⚙️ 可以选择是否包含几何信息
- 📊 生成详细的转换日志
- 🔍 提供转换统计和错误报告

**交互式选项：**
1. 输入目录选择（默认当前目录）
2. 输出目录选择（默认原文件目录）
3. 几何信息包含选项
4. 转换确认

## 📋 转换结果

### 输出文件格式

每个 `.shp` 文件会生成对应的 `.csv` 文件，包含：

**基本属性数据：**
- 原shp文件中的所有属性字段

**几何信息（如果启用）：**
- `geometry_wkt`: 几何的WKT文本表示

**点几何额外字段：**
- `longitude`: 经度
- `latitude`: 纬度

**面几何额外字段：**
- `centroid_lon`: 质心经度
- `centroid_lat`: 质心纬度

### 示例输出

假设您有以下shp文件：
```
📁 当前目录/
  ├── stations.shp
  ├── roads.shp
  └── districts.shp
```

转换后会生成：
```
📁 当前目录/
  ├── stations.shp
  ├── stations.csv      ← 新生成
  ├── roads.shp
  ├── roads.csv         ← 新生成
  ├── districts.shp
  └── districts.csv     ← 新生成
```

## 🛠️ 依赖库

确保安装了以下Python库：

```bash
pip install geopandas pandas
```

如果遇到geopandas安装问题，可以尝试：

```bash
# 使用conda安装（推荐）
conda install geopandas

# 或者使用pip安装完整依赖
pip install geopandas[complete]
```

## 📊 转换示例

### 示例1：站点数据转换

**输入 (stations.shp):**
- 几何类型：Point
- 属性：station_id, station_name, line

**输出 (stations.csv):**
```csv
station_id,station_name,line,longitude,latitude,geometry_wkt
1,北京站,1号线,116.4074,39.9042,"POINT (116.4074 39.9042)"
2,天安门东,1号线,116.4109,39.9097,"POINT (116.4109 39.9097)"
```

### 示例2：区域数据转换

**输入 (districts.shp):**
- 几何类型：Polygon
- 属性：district_id, district_name, area

**输出 (districts.csv):**
```csv
district_id,district_name,area,centroid_lon,centroid_lat,geometry_wkt
1,朝阳区,454.78,116.4430,39.9219,"POLYGON ((116.123 39.456, ...))"
2,海淀区,430.77,116.2988,39.9594,"POLYGON ((116.089 39.678, ...))"
```

## ⚠️ 注意事项

1. **文件权限**：确保对目录有读写权限
2. **文件大小**：大型shp文件可能需要较长转换时间
3. **编码问题**：CSV文件使用UTF-8编码保存
4. **几何复杂度**：复杂几何的WKT表示可能很长
5. **坐标系统**：保持原始坐标系统，不进行投影转换

## 🔍 故障排除

### 常见错误及解决方案

**错误1：ModuleNotFoundError: No module named 'geopandas'**
```bash
# 解决方案
pip install geopandas pandas
```

**错误2：无法读取shp文件**
- 检查shp文件是否完整（需要.shp, .shx, .dbf等文件）
- 检查文件是否损坏

**错误3：权限错误**
- 确保对目录有写权限
- 检查文件是否被其他程序占用

**错误4：编码问题**
- 脚本使用UTF-8编码保存CSV
- 如需其他编码，可修改脚本中的encoding参数

## 📞 使用建议

1. **首次使用**：建议先用小文件测试
2. **批量转换**：使用 `quick_shp_to_csv.py` 最方便
3. **特殊需求**：使用 `shp_to_csv_converter.py` 获得更多控制
4. **备份数据**：转换前建议备份原始shp文件
5. **检查结果**：转换后检查CSV文件内容是否正确

## 🎯 快速开始

1. 将脚本放在包含shp文件的目录中
2. 运行：`python quick_shp_to_csv.py`
3. 查看生成的CSV文件

就这么简单！🎉
