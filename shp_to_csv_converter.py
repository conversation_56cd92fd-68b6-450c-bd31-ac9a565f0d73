"""
Shapefile to CSV Converter
将目录中的所有shp文件转换为csv文件

功能：
1. 自动扫描指定目录中的所有.shp文件
2. 将每个shp文件转换为对应的csv文件
3. 保留所有属性数据和几何信息
4. 支持批量转换
5. 提供详细的转换日志

使用方法：
python shp_to_csv_converter.py
"""

import os
import pandas as pd
import geopandas as gpd
from pathlib import Path
import sys
import traceback
from datetime import datetime

def find_shp_files(directory):
    """
    在指定目录中查找所有.shp文件
    
    Args:
        directory (str): 要搜索的目录路径
        
    Returns:
        list: .shp文件路径列表
    """
    shp_files = []
    directory_path = Path(directory)
    
    if not directory_path.exists():
        print(f"错误：目录 '{directory}' 不存在")
        return shp_files
    
    # 查找所有.shp文件
    for shp_file in directory_path.glob("*.shp"):
        shp_files.append(str(shp_file))
    
    return shp_files

def convert_shp_to_csv(shp_file_path, output_dir=None, include_geometry=True):
    """
    将单个shp文件转换为csv文件
    
    Args:
        shp_file_path (str): shp文件路径
        output_dir (str): 输出目录，如果为None则输出到原文件目录
        include_geometry (bool): 是否包含几何信息
        
    Returns:
        tuple: (success, output_file_path, error_message)
    """
    try:
        # 读取shapefile
        print(f"正在读取: {shp_file_path}")
        gdf = gpd.read_file(shp_file_path)
        
        # 获取文件名（不含扩展名）
        file_name = Path(shp_file_path).stem
        
        # 确定输出目录
        if output_dir is None:
            output_dir = Path(shp_file_path).parent
        else:
            output_dir = Path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
        
        # 构建输出文件路径
        csv_file_path = output_dir / f"{file_name}.csv"
        
        # 处理几何信息
        if include_geometry and 'geometry' in gdf.columns:
            # 添加几何信息的文本表示
            gdf['geometry_wkt'] = gdf['geometry'].apply(lambda x: x.wkt if x is not None else None)
            
            # 如果是点几何，提取坐标
            if not gdf.empty and gdf.geometry.iloc[0] is not None:
                geom_type = gdf.geometry.iloc[0].geom_type
                if geom_type == 'Point':
                    gdf['longitude'] = gdf.geometry.x
                    gdf['latitude'] = gdf.geometry.y
                elif geom_type in ['Polygon', 'MultiPolygon']:
                    # 计算质心坐标
                    centroids = gdf.geometry.centroid
                    gdf['centroid_longitude'] = centroids.x
                    gdf['centroid_latitude'] = centroids.y
        
        # 转换为DataFrame（移除几何列）
        if 'geometry' in gdf.columns:
            df = pd.DataFrame(gdf.drop(columns=['geometry']))
        else:
            df = pd.DataFrame(gdf)
        
        # 保存为CSV
        df.to_csv(csv_file_path, index=False, encoding='utf-8-sig')
        
        print(f"✓ 转换成功: {csv_file_path}")
        print(f"  - 记录数: {len(df)}")
        print(f"  - 列数: {len(df.columns)}")
        if not df.empty:
            print(f"  - 列名: {', '.join(df.columns.tolist())}")
        
        return True, str(csv_file_path), None
        
    except Exception as e:
        error_msg = f"转换失败: {str(e)}"
        print(f"✗ {error_msg}")
        return False, None, error_msg

def batch_convert_shp_to_csv(directory, output_dir=None, include_geometry=True):
    """
    批量转换目录中的所有shp文件
    
    Args:
        directory (str): 包含shp文件的目录
        output_dir (str): 输出目录
        include_geometry (bool): 是否包含几何信息
        
    Returns:
        dict: 转换结果统计
    """
    print("=" * 60)
    print("Shapefile to CSV 批量转换工具")
    print("=" * 60)
    print(f"扫描目录: {directory}")
    
    # 查找所有shp文件
    shp_files = find_shp_files(directory)
    
    if not shp_files:
        print("未找到任何.shp文件")
        return {"total": 0, "success": 0, "failed": 0, "results": []}
    
    print(f"找到 {len(shp_files)} 个shp文件:")
    for shp_file in shp_files:
        print(f"  - {Path(shp_file).name}")
    
    print("\n开始转换...")
    print("-" * 40)
    
    # 转换统计
    results = {
        "total": len(shp_files),
        "success": 0,
        "failed": 0,
        "results": []
    }
    
    # 逐个转换
    for i, shp_file in enumerate(shp_files, 1):
        print(f"\n[{i}/{len(shp_files)}] 处理: {Path(shp_file).name}")
        
        success, output_file, error_msg = convert_shp_to_csv(
            shp_file, output_dir, include_geometry
        )
        
        result_info = {
            "input_file": shp_file,
            "output_file": output_file,
            "success": success,
            "error": error_msg
        }
        results["results"].append(result_info)
        
        if success:
            results["success"] += 1
        else:
            results["failed"] += 1
    
    return results

def print_summary(results):
    """打印转换结果摘要"""
    print("\n" + "=" * 60)
    print("转换完成摘要")
    print("=" * 60)
    print(f"总文件数: {results['total']}")
    print(f"成功转换: {results['success']}")
    print(f"转换失败: {results['failed']}")
    
    if results['failed'] > 0:
        print("\n失败的文件:")
        for result in results['results']:
            if not result['success']:
                print(f"  - {Path(result['input_file']).name}: {result['error']}")
    
    if results['success'] > 0:
        print(f"\n成功转换的文件保存在:")
        for result in results['results']:
            if result['success']:
                print(f"  - {result['output_file']}")

def main():
    """主函数"""
    # 默认使用当前目录
    current_dir = os.getcwd()
    
    print("Shapefile to CSV 转换工具")
    print("=" * 40)
    
    # 获取用户输入
    input_dir = input(f"请输入包含shp文件的目录路径 (回车使用当前目录: {current_dir}): ").strip()
    if not input_dir:
        input_dir = current_dir
    
    # 检查目录是否存在
    if not os.path.exists(input_dir):
        print(f"错误：目录 '{input_dir}' 不存在")
        return
    
    # 输出目录选项
    output_dir = input("请输入输出目录路径 (回车使用原文件目录): ").strip()
    if not output_dir:
        output_dir = None
    
    # 几何信息选项
    include_geom = input("是否包含几何信息? (y/n, 默认y): ").strip().lower()
    include_geometry = include_geom != 'n'
    
    print(f"\n配置:")
    print(f"  输入目录: {input_dir}")
    print(f"  输出目录: {output_dir if output_dir else '原文件目录'}")
    print(f"  包含几何信息: {'是' if include_geometry else '否'}")
    
    # 确认开始转换
    confirm = input("\n开始转换? (y/n): ").strip().lower()
    if confirm != 'y':
        print("转换已取消")
        return
    
    # 执行批量转换
    try:
        results = batch_convert_shp_to_csv(input_dir, output_dir, include_geometry)
        print_summary(results)
        
        # 保存转换日志
        log_file = Path(input_dir) / f"conversion_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(f"Shapefile to CSV 转换日志\n")
            f.write(f"转换时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"输入目录: {input_dir}\n")
            f.write(f"输出目录: {output_dir if output_dir else '原文件目录'}\n")
            f.write(f"包含几何信息: {'是' if include_geometry else '否'}\n\n")
            f.write(f"总文件数: {results['total']}\n")
            f.write(f"成功转换: {results['success']}\n")
            f.write(f"转换失败: {results['failed']}\n\n")
            
            f.write("详细结果:\n")
            for result in results['results']:
                f.write(f"  {Path(result['input_file']).name}: ")
                if result['success']:
                    f.write(f"成功 -> {result['output_file']}\n")
                else:
                    f.write(f"失败 - {result['error']}\n")
        
        print(f"\n转换日志已保存到: {log_file}")
        
    except Exception as e:
        print(f"转换过程中发生错误: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
