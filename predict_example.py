#!/usr/bin/env python3
"""
Metro Flow Prediction Example
使用训练好的模型进行地铁客流预测的示例脚本

Usage:
    python predict_example.py
"""

from metro_predictor import MetroFlowPredictor
import pandas as pd

def main():
    print("Metro Flow Prediction Example")
    print("=" * 40)
    
    # 初始化预测器
    try:
        predictor = MetroFlowPredictor()
        print("✓ Predictor initialized successfully!")
    except Exception as e:
        print(f"✗ Failed to initialize predictor: {e}")
        return
    
    # 获取可用站点列表
    stations = predictor.get_available_stations()
    print(f"\n📍 Available stations: {len(stations)} total")
    print(f"First 10 stations: {stations[:10]}")
    
    # 示例1: 单个站点客流预测
    print("\n" + "="*40)
    print("Example 1: Single Station Flow Prediction")
    print("="*40)
    
    if len(stations) > 0:
        test_station = stations[0]  # 使用第一个可用站点
        test_hour = 8  # 早高峰
        
        in_flow, out_flow = predictor.predict_station_flow(test_station, test_hour)
        print(f"Station: {test_station}")
        print(f"Hour: {test_hour}")
        print(f"Predicted Inflow: {in_flow:.2f}")
        print(f"Predicted Outflow: {out_flow:.2f}")
    
    # 示例2: 批量站点预测
    print("\n" + "="*40)
    print("Example 2: Batch Station Flow Prediction")
    print("="*40)
    
    # 选择前3个站点和几个时间点
    test_stations = stations[:3]
    test_hours = [7, 8, 9, 17, 18, 19]  # 早晚高峰
    
    batch_predictions = predictor.predict_station_flows_batch(test_stations, test_hours)
    print(f"Predictions for {len(test_stations)} stations at {len(test_hours)} hours:")
    print(batch_predictions.head(10))
    
    # 保存结果
    predictor.save_predictions(batch_predictions, 'example_station_predictions.csv')
    
    # 示例3: OD流量预测
    print("\n" + "="*40)
    print("Example 3: OD Flow Prediction")
    print("="*40)
    
    if len(stations) >= 2:
        origin = stations[0]
        destination = stations[1]
        test_hour = 8
        
        od_flow = predictor.predict_od_flow(origin, destination, test_hour)
        print(f"Origin: {origin}")
        print(f"Destination: {destination}")
        print(f"Hour: {test_hour}")
        print(f"Predicted OD Flow: {od_flow:.2f}")
    
    # 示例4: 批量OD预测
    print("\n" + "="*40)
    print("Example 4: Batch OD Flow Prediction")
    print("="*40)
    
    if len(stations) >= 4:
        # 创建一些OD对
        od_pairs = [
            (stations[0], stations[1]),
            (stations[1], stations[2]),
            (stations[2], stations[3]),
            (stations[3], stations[0])
        ]
        test_hours = [8, 12, 18]
        
        od_predictions = predictor.predict_od_flows_batch(od_pairs, test_hours)
        print(f"Predictions for {len(od_pairs)} OD pairs at {len(test_hours)} hours:")
        print(od_predictions.head(10))
        
        # 保存结果
        predictor.save_predictions(od_predictions, 'example_od_predictions.csv')
    
    # 示例5: 一天24小时的预测
    print("\n" + "="*40)
    print("Example 5: 24-Hour Prediction for One Station")
    print("="*40)
    
    if len(stations) > 0:
        test_station = stations[0]
        all_hours = list(range(24))
        
        daily_predictions = predictor.predict_station_flows_batch([test_station], all_hours)
        print(f"24-hour predictions for {test_station}:")
        print(daily_predictions)
        
        # 保存结果
        predictor.save_predictions(daily_predictions, f'daily_prediction_{test_station.replace("/", "_")}.csv')
    
    print("\n" + "="*40)
    print("✓ All examples completed successfully!")
    print("Check the generated CSV files for detailed results.")

def custom_prediction():
    """
    自定义预测函数 - 用户可以修改这个函数来进行特定的预测
    """
    print("\nCustom Prediction Function")
    print("-" * 30)
    
    predictor = MetroFlowPredictor()
    
    # 在这里添加您的自定义预测逻辑
    # 例如：
    
    # 1. 预测特定站点在特定时间的客流
    # station_name = "您的站点名称"
    # hour = 8
    # in_flow, out_flow = predictor.predict_station_flow(station_name, hour)
    
    # 2. 预测特定OD对的客流
    # origin = "起点站点"
    # destination = "终点站点"
    # od_flow = predictor.predict_od_flow(origin, destination, hour)
    
    # 3. 批量预测
    # stations = ["站点1", "站点2", "站点3"]
    # hours = [7, 8, 9, 17, 18, 19]
    # predictions = predictor.predict_station_flows_batch(stations, hours)
    
    print("Add your custom prediction logic here!")

if __name__ == '__main__':
    try:
        main()
        
        # 如果需要运行自定义预测，取消下面的注释
        # custom_prediction()
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Prediction interrupted by user")
    except Exception as e:
        print(f"\n❌ Error occurred: {e}")
        import traceback
        traceback.print_exc()
