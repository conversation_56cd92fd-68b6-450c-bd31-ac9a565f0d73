# Metro Flow Predictor - 地铁客流预测器

## 概述 (Overview)

这是一个独立的地铁客流预测系统，使用已经训练好的深度学习模型进行预测。支持站点客流预测和OD（起点-终点）客流预测。

## 文件结构 (File Structure)

```
├── metro_predictor.py      # 主预测器类和模型定义
├── predict_example.py      # 使用示例脚本
├── PREDICTION_README.md    # 本说明文档
└── 依赖的数据文件:
    ├── best_advanced_metro_model.pth    # 训练好的模型权重
    ├── station_connect_2023.csv         # 站点连接数据
    ├── leti_data.csv                    # 网格特征数据
    ├── in_500_with_coords.shp           # 空间数据文件
    ├── in_500.csv                       # 进站流量数据（用于拟合标准化器）
    └── updated北京市_subway_od_2024_modified3_deduplicated.csv  # OD数据
```

## 快速开始 (Quick Start)

### 1. 运行示例

```bash
python predict_example.py
```

这将运行所有预设的示例，包括：
- 单个站点客流预测
- 批量站点预测
- OD流量预测
- 24小时预测

### 2. 基本使用

```python
from metro_predictor import MetroFlowPredictor

# 初始化预测器
predictor = MetroFlowPredictor()

# 获取可用站点
stations = predictor.get_available_stations()
print(f"Available stations: {len(stations)}")

# 预测单个站点客流
station = "北京站"  # 替换为实际站点名
hour = 8  # 早上8点
in_flow, out_flow = predictor.predict_station_flow(station, hour)
print(f"Predicted inflow: {in_flow:.2f}")
print(f"Predicted outflow: {out_flow:.2f}")

# 预测OD流量
origin = "北京站"
destination = "西直门"
od_flow = predictor.predict_od_flow(origin, destination, hour)
print(f"Predicted OD flow: {od_flow:.2f}")
```

## API 文档 (API Documentation)

### MetroFlowPredictor 类

#### 初始化
```python
predictor = MetroFlowPredictor(model_path="path/to/model.pth")
```

#### 主要方法

**1. predict_station_flow(station, hour)**
- 预测指定站点在指定时间的进出客流
- 参数:
  - `station` (str): 站点名称
  - `hour` (int): 小时 (0-23 或绝对小时)
- 返回: `(inflow, outflow)` 元组

**2. predict_od_flow(origin, dest, hour, ...)**
- 预测两个站点间的OD流量
- 参数:
  - `origin` (str): 起点站点
  - `dest` (str): 终点站点
  - `hour` (int): 小时
  - `surface_distance` (float, 可选): 地面距离
  - `translate` (float, 可选): 换乘距离
  - `time` (float, 可选): 行程时间
  - `wait_time` (float, 可选): 等待时间
- 返回: `float` OD流量预测值

**3. predict_station_flows_batch(stations, hours)**
- 批量预测多个站点多个时间的客流
- 参数:
  - `stations` (list): 站点名称列表
  - `hours` (list): 小时列表
- 返回: `pandas.DataFrame` 包含所有预测结果

**4. predict_od_flows_batch(od_pairs, hours)**
- 批量预测多个OD对的流量
- 参数:
  - `od_pairs` (list): (起点, 终点) 元组列表
  - `hours` (list): 小时列表
- 返回: `pandas.DataFrame` 包含所有预测结果

**5. get_available_stations()**
- 获取所有可用站点列表
- 返回: `list` 站点名称列表

**6. save_predictions(df, filename)**
- 保存预测结果到CSV文件
- 参数:
  - `df` (pandas.DataFrame): 预测结果
  - `filename` (str): 文件名

## 使用示例 (Usage Examples)

### 示例1: 高峰时段分析
```python
predictor = MetroFlowPredictor()

# 分析早高峰时段 (7-9点) 的客流
peak_hours = [7, 8, 9]
key_stations = ["北京站", "西直门", "国贸"]

predictions = predictor.predict_station_flows_batch(key_stations, peak_hours)
print("Peak hour analysis:")
print(predictions.groupby('hour')[['predicted_inflow', 'predicted_outflow']].mean())
```

### 示例2: 站点间流量分析
```python
# 分析主要站点间的流量
major_stations = ["北京站", "西直门", "国贸", "中关村"]
od_pairs = []

# 生成所有站点对
for i, origin in enumerate(major_stations):
    for j, dest in enumerate(major_stations):
        if i != j:
            od_pairs.append((origin, dest))

# 预测不同时段的OD流量
time_periods = [8, 12, 18]  # 早高峰、午间、晚高峰
od_predictions = predictor.predict_od_flows_batch(od_pairs, time_periods)

# 分析结果
print("Top OD pairs by predicted flow:")
top_flows = od_predictions.groupby(['origin', 'destination'])['predicted_od_flow'].mean().sort_values(ascending=False)
print(top_flows.head(10))
```

### 示例3: 24小时客流模式
```python
# 分析某个站点的24小时客流模式
station = "北京站"
all_hours = list(range(24))

daily_pattern = predictor.predict_station_flows_batch([station], all_hours)

# 可视化（需要安装matplotlib）
import matplotlib.pyplot as plt
plt.figure(figsize=(12, 6))
plt.plot(daily_pattern['hour'], daily_pattern['predicted_inflow'], label='Inflow', marker='o')
plt.plot(daily_pattern['hour'], daily_pattern['predicted_outflow'], label='Outflow', marker='s')
plt.xlabel('Hour of Day')
plt.ylabel('Predicted Flow')
plt.title(f'24-Hour Flow Pattern for {station}')
plt.legend()
plt.grid(True)
plt.show()
```

## 注意事项 (Important Notes)

1. **模型依赖**: 确保 `best_advanced_metro_model.pth` 文件存在且是正确的模型权重

2. **数据文件**: 所有必需的数据文件必须在正确的位置

3. **站点名称**: 使用 `get_available_stations()` 获取正确的站点名称

4. **时间格式**: 
   - 小时可以是 0-23 (一天中的小时)
   - 或者是绝对小时数 (用于表示不同的天)

5. **预测范围**: 预测结果基于训练数据的分布，超出正常范围的输入可能产生不准确的结果

6. **性能**: 批量预测比单个预测更高效

## 故障排除 (Troubleshooting)

### 常见错误

1. **模型文件未找到**
   ```
   FileNotFoundError: Model file not found
   ```
   解决: 确保 `best_advanced_metro_model.pth` 在正确位置

2. **站点未找到**
   ```
   Warning: Station 'XXX' not found in station index
   ```
   解决: 使用 `get_available_stations()` 查看可用站点

3. **数据文件缺失**
   ```
   Error loading data: [Errno 2] No such file or directory
   ```
   解决: 确保所有必需的数据文件都存在

### 调试模式

在代码中添加更多调试信息：
```python
import logging
logging.basicConfig(level=logging.DEBUG)

predictor = MetroFlowPredictor()
```

## 扩展功能 (Extensions)

您可以通过以下方式扩展预测器：

1. **添加新的特征**: 修改特征提取函数
2. **自定义预处理**: 修改数据加载和预处理逻辑
3. **批量处理**: 添加更大规模的批量预测功能
4. **结果分析**: 添加统计分析和可视化功能

## 联系支持 (Support)

如果遇到问题或需要帮助，请检查：
1. 所有依赖文件是否完整
2. Python环境是否正确配置
3. 模型文件是否损坏

---

**版本**: 1.0  
**最后更新**: 2025-01-10
