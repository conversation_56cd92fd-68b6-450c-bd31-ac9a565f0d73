"""
快速 Shapefile 到 CSV 转换器
自动转换当前目录中的所有.shp文件为.csv文件

使用方法：
python quick_shp_to_csv.py
"""

import os
import pandas as pd
import geopandas as gpd
from pathlib import Path
import sys

def convert_all_shp_in_current_dir():
    """转换当前目录中的所有shp文件"""
    current_dir = Path.cwd()
    print(f"正在扫描目录: {current_dir}")
    
    # 查找所有.shp文件
    shp_files = list(current_dir.glob("*.shp"))
    
    if not shp_files:
        print("❌ 当前目录中未找到任何.shp文件")
        return
    
    print(f"✅ 找到 {len(shp_files)} 个shp文件:")
    for shp_file in shp_files:
        print(f"   📁 {shp_file.name}")
    
    print("\n🔄 开始转换...")
    print("-" * 50)
    
    success_count = 0
    failed_count = 0
    
    for shp_file in shp_files:
        try:
            print(f"\n📖 正在处理: {shp_file.name}")
            
            # 读取shapefile
            gdf = gpd.read_file(shp_file)
            print(f"   📊 记录数: {len(gdf)}")
            print(f"   📋 列数: {len(gdf.columns)}")
            
            # 处理几何信息
            if 'geometry' in gdf.columns and not gdf.empty:
                # 添加几何类型信息
                if gdf.geometry.iloc[0] is not None:
                    geom_type = gdf.geometry.iloc[0].geom_type
                    print(f"   🗺️  几何类型: {geom_type}")
                    
                    # 如果是点，提取坐标
                    if geom_type == 'Point':
                        gdf['longitude'] = gdf.geometry.x
                        gdf['latitude'] = gdf.geometry.y
                        print(f"   📍 已添加经纬度列")
                    
                    # 如果是面，计算质心
                    elif geom_type in ['Polygon', 'MultiPolygon']:
                        centroids = gdf.geometry.centroid
                        gdf['centroid_lon'] = centroids.x
                        gdf['centroid_lat'] = centroids.y
                        print(f"   🎯 已添加质心坐标列")
                
                # 添加几何的WKT表示
                gdf['geometry_wkt'] = gdf['geometry'].apply(
                    lambda x: x.wkt if x is not None else None
                )
                print(f"   📝 已添加几何WKT列")
            
            # 转换为DataFrame（移除几何列）
            if 'geometry' in gdf.columns:
                df = pd.DataFrame(gdf.drop(columns=['geometry']))
            else:
                df = pd.DataFrame(gdf)
            
            # 生成CSV文件名
            csv_file = shp_file.with_suffix('.csv')
            
            # 保存为CSV
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            
            print(f"   ✅ 转换成功: {csv_file.name}")
            print(f"   📄 最终列数: {len(df.columns)}")
            if len(df.columns) <= 10:  # 如果列不多，显示列名
                print(f"   📋 列名: {', '.join(df.columns.tolist())}")
            
            success_count += 1
            
        except Exception as e:
            print(f"   ❌ 转换失败: {str(e)}")
            failed_count += 1
    
    # 打印总结
    print("\n" + "=" * 50)
    print("🎉 转换完成!")
    print("=" * 50)
    print(f"📊 总文件数: {len(shp_files)}")
    print(f"✅ 成功转换: {success_count}")
    print(f"❌ 转换失败: {failed_count}")
    
    if success_count > 0:
        print(f"\n📁 CSV文件已保存在当前目录:")
        for shp_file in shp_files:
            csv_file = shp_file.with_suffix('.csv')
            if csv_file.exists():
                print(f"   📄 {csv_file.name}")

def main():
    """主函数"""
    print("🚀 快速 Shapefile 到 CSV 转换器")
    print("=" * 50)
    
    try:
        # 检查是否安装了必要的库
        import geopandas
        import pandas
        print("✅ 依赖库检查通过")
    except ImportError as e:
        print(f"❌ 缺少必要的库: {e}")
        print("请安装: pip install geopandas pandas")
        return
    
    # 执行转换
    convert_all_shp_in_current_dir()
    
    print(f"\n🎯 提示: 如需更多选项，请使用 shp_to_csv_converter.py")

if __name__ == "__main__":
    main()
